import unittest
import pandas as pd
import numpy as np
from datetime import datetime, date
import sys
import os

# Add the parent directory to the path to import woo_utils
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import the specific module directly to avoid dependency issues
import importlib.util
spec = importlib.util.spec_from_file_location("df_tools", os.path.join(os.path.dirname(__file__), '..', 'woo_utils', 'df_tools.py'))
df_tools = importlib.util.module_from_spec(spec)
spec.loader.exec_module(df_tools)
merge_df = df_tools.merge_df
equation_to_df = df_tools.equation_to_df
check_dates = df_tools.check_dates


class TestMergeDF(unittest.TestCase):
    """Test cases for the merge_df function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample DataFrames for testing
        self.df1 = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02', '2023-01-03'],
            'value': [10, 20, 30]
        })
        
        self.df2 = pd.DataFrame({
            'date': ['2023-01-04', '2023-01-05'],
            'value': [40, 50]
        })
        
        self.df3 = pd.DataFrame({
            'date': ['2023-01-06'],
            'value': [60]
        })
        
        # DataFrame with different data types
        self.df_float = pd.DataFrame({
            'date': ['2023-02-01', '2023-02-02'],
            'value': [1.5, 2.7]
        })
        
        # DataFrame with datetime objects
        self.df_datetime = pd.DataFrame({
            'date': [datetime(2023, 3, 1), datetime(2023, 3, 2)],
            'value': [100, 200]
        })
        
        # Empty DataFrame
        self.df_empty = pd.DataFrame({
            'date': [],
            'value': []
        })

    def test_merge_two_dataframes(self):
        """Test merging two DataFrames with basic functionality"""
        result = merge_df(self.df1, 'type1', self.df2, 'type2')
        
        # Check structure
        self.assertEqual(list(result.columns), ['date', 'type', 'value'])
        self.assertEqual(len(result), 5)  # 3 + 2 rows
        
        # Check data integrity
        self.assertEqual(result.iloc[0]['date'], '2023-01-01')
        self.assertEqual(result.iloc[0]['type'], 'type1')
        self.assertEqual(result.iloc[0]['value'], 10)
        
        self.assertEqual(result.iloc[3]['date'], '2023-01-04')
        self.assertEqual(result.iloc[3]['type'], 'type2')
        self.assertEqual(result.iloc[3]['value'], 40)

    def test_merge_multiple_dataframes(self):
        """Test merging more than two DataFrames"""
        result = merge_df(self.df1, 'type1', self.df2, 'type2', self.df3, 'type3')
        
        # Check structure
        self.assertEqual(list(result.columns), ['date', 'type', 'value'])
        self.assertEqual(len(result), 6)  # 3 + 2 + 1 rows
        
        # Check that all types are present
        types = result['type'].unique()
        self.assertIn('type1', types)
        self.assertIn('type2', types)
        self.assertIn('type3', types)
        
        # Check last row
        self.assertEqual(result.iloc[-1]['date'], '2023-01-06')
        self.assertEqual(result.iloc[-1]['type'], 'type3')
        self.assertEqual(result.iloc[-1]['value'], 60)

    def test_merge_with_different_data_types(self):
        """Test merging DataFrames with different value data types"""
        result = merge_df(self.df1, 'integers', self.df_float, 'floats')
        
        # Check structure
        self.assertEqual(len(result), 5)
        
        # Check that both integer and float values are preserved
        integer_rows = result[result['type'] == 'integers']
        float_rows = result[result['type'] == 'floats']
        
        self.assertEqual(len(integer_rows), 3)
        self.assertEqual(len(float_rows), 2)
        
        # Check specific values
        self.assertEqual(float_rows.iloc[0]['value'], 1.5)
        self.assertEqual(float_rows.iloc[1]['value'], 2.7)

    def test_merge_with_datetime_objects(self):
        """Test merging DataFrames with datetime objects"""
        result = merge_df(self.df1, 'strings', self.df_datetime, 'datetimes')
        
        # Check structure
        self.assertEqual(len(result), 5)
        
        # Check datetime preservation
        datetime_rows = result[result['type'] == 'datetimes']
        self.assertEqual(len(datetime_rows), 2)

    def test_merge_with_empty_dataframe(self):
        """Test merging with an empty DataFrame"""
        result = merge_df(self.df1, 'type1', self.df_empty, 'empty')
        
        # Check structure - should only have rows from df1
        self.assertEqual(len(result), 3)
        
        # All rows should be of type1
        self.assertTrue(all(result['type'] == 'type1'))

    def test_merge_single_row_dataframes(self):
        """Test merging DataFrames with single rows"""
        df_single1 = pd.DataFrame({'date': ['2023-01-01'], 'value': [100]})
        df_single2 = pd.DataFrame({'date': ['2023-01-02'], 'value': [200]})
        
        result = merge_df(df_single1, 'single1', df_single2, 'single2')
        
        # Check structure
        self.assertEqual(len(result), 2)
        self.assertEqual(result.iloc[0]['type'], 'single1')
        self.assertEqual(result.iloc[1]['type'], 'single2')

    def test_original_dataframes_not_modified(self):
        """Test that original DataFrames are not modified"""
        original_df1 = self.df1.copy()
        original_df2 = self.df2.copy()
        
        merge_df(self.df1, 'type1', self.df2, 'type2')
        
        # Check that original DataFrames are unchanged
        pd.testing.assert_frame_equal(self.df1, original_df1)
        pd.testing.assert_frame_equal(self.df2, original_df2)
        
        # Check that 'type' column was not added to originals
        self.assertNotIn('type', self.df1.columns)
        self.assertNotIn('type', self.df2.columns)

    def test_error_odd_number_of_arguments(self):
        """Test that ValueError is raised for odd number of arguments"""
        with self.assertRaises(ValueError) as context:
            merge_df(self.df1, 'type1', self.df2)
        
        self.assertIn("必须成对传入 df 和 type 参数", str(context.exception))

    def test_error_non_dataframe_argument(self):
        """Test that TypeError is raised for non-DataFrame arguments"""
        with self.assertRaises(TypeError) as context:
            merge_df("not_a_dataframe", 'type1', self.df2, 'type2')
        
        self.assertIn("不是一个有效的 DataFrame", str(context.exception))

    def test_error_non_dataframe_in_middle(self):
        """Test error handling when non-DataFrame appears in middle of arguments"""
        with self.assertRaises(TypeError) as context:
            merge_df(self.df1, 'type1', [1, 2, 3], 'type2')
        
        self.assertIn("不是一个有效的 DataFrame", str(context.exception))

    def test_dataframes_with_different_column_names(self):
        """Test merging DataFrames with different column names (should still work)"""
        df_different_cols = pd.DataFrame({
            'timestamp': ['2023-01-01', '2023-01-02'],
            'amount': [100, 200]
        })
        
        # This should work as the function uses positional columns
        result = merge_df(self.df1, 'type1', df_different_cols, 'type2')
        
        # Check structure
        self.assertEqual(len(result), 5)
        self.assertEqual(list(result.columns), ['date', 'type', 'value'])

    def test_column_order_preservation(self):
        """Test that the order of data is preserved correctly"""
        result = merge_df(self.df1, 'first', self.df2, 'second')
        
        # First 3 rows should be from df1
        first_part = result.iloc[:3]
        self.assertTrue(all(first_part['type'] == 'first'))
        self.assertEqual(list(first_part['value']), [10, 20, 30])
        
        # Last 2 rows should be from df2
        second_part = result.iloc[3:]
        self.assertTrue(all(second_part['type'] == 'second'))
        self.assertEqual(list(second_part['value']), [40, 50])

    def test_empty_arguments(self):
        """Test behavior with no arguments"""
        result = merge_df()

        # Should return empty DataFrame with correct structure
        self.assertEqual(list(result.columns), ['date', 'type', 'value'])
        self.assertEqual(len(result), 0)
        self.assertTrue(result.empty)


class TestEquationToDF(unittest.TestCase):
    """Test cases for the equation_to_df function"""

    def test_linear_equation(self):
        """Test basic linear equation: y = 2*x + 1"""
        result = equation_to_df("y = 2*x + 1", 0, 5, 1)

        # Check structure
        self.assertEqual(list(result.columns), ['x', 'y'])
        self.assertEqual(len(result), 6)  # 0, 1, 2, 3, 4, 5

        # Check values
        expected_x = [0, 1, 2, 3, 4, 5]
        expected_y = [1, 3, 5, 7, 9, 11]  # 2*x + 1

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_quadratic_equation(self):
        """Test quadratic equation: y = x^2"""
        result = equation_to_df("y = x^2", -2, 2, 1)

        # Check structure
        self.assertEqual(list(result.columns), ['x', 'y'])
        self.assertEqual(len(result), 5)  # -2, -1, 0, 1, 2

        # Check values
        expected_x = [-2, -1, 0, 1, 2]
        expected_y = [4, 1, 0, 1, 4]  # x^2

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_equation_without_y_equals(self):
        """Test equation without 'y =' prefix"""
        result = equation_to_df("3*x - 2", 1, 3, 1)

        # Check structure
        self.assertEqual(list(result.columns), ['x', 'y'])
        self.assertEqual(len(result), 3)  # 1, 2, 3

        # Check values
        expected_x = [1, 2, 3]
        expected_y = [1, 4, 7]  # 3*x - 2

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_implicit_multiplication(self):
        """Test implicit multiplication: 2x + 3"""
        result = equation_to_df("2x + 3", 0, 2, 1)

        # Check values
        expected_x = [0, 1, 2]
        expected_y = [3, 5, 7]  # 2*x + 3

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_sqrt_function(self):
        """Test square root function: sqrt(x)"""
        result = equation_to_df("sqrt(x)", 0, 4, 1)

        # Check values
        expected_x = [0, 1, 2, 3, 4]
        expected_y = [0, 1, np.sqrt(2), np.sqrt(3), 2]

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_almost_equal(result['y'].values, expected_y)

    def test_trigonometric_functions(self):
        """Test trigonometric functions: sin(x)"""
        result = equation_to_df("sin(x)", 0, np.pi, np.pi/2)

        # Check structure
        self.assertEqual(list(result.columns), ['x', 'y'])
        self.assertEqual(len(result), 3)  # 0, π/2, π

        # Check approximate values
        expected_y = [0, 1, 0]  # sin(0), sin(π/2), sin(π)
        np.testing.assert_array_almost_equal(result['y'].values, expected_y, decimal=10)

    def test_complex_equation(self):
        """Test complex equation: 2*x^2 + 3*x - 1"""
        result = equation_to_df("2*x^2 + 3*x - 1", -1, 1, 0.5)

        # Check structure
        self.assertEqual(list(result.columns), ['x', 'y'])
        self.assertEqual(len(result), 5)  # -1, -0.5, 0, 0.5, 1

        # Check some values
        x_vals = result['x'].values
        y_vals = result['y'].values

        # Verify calculation for x = 0: 2*0^2 + 3*0 - 1 = -1
        zero_idx = np.where(x_vals == 0)[0][0]
        self.assertAlmostEqual(y_vals[zero_idx], -1)

        # Verify calculation for x = 1: 2*1^2 + 3*1 - 1 = 4
        one_idx = np.where(x_vals == 1)[0][0]
        self.assertAlmostEqual(y_vals[one_idx], 4)

    def test_decimal_interval(self):
        """Test with decimal interval"""
        result = equation_to_df("x", 0, 1, 0.25)

        # Check structure
        self.assertEqual(len(result), 5)  # 0, 0.25, 0.5, 0.75, 1

        expected_x = [0, 0.25, 0.5, 0.75, 1.0]
        np.testing.assert_array_almost_equal(result['x'].values, expected_x)
        np.testing.assert_array_almost_equal(result['y'].values, expected_x)  # y = x

    def test_negative_values(self):
        """Test with negative start and end values"""
        result = equation_to_df("x^2", -3, -1, 1)

        # Check structure
        self.assertEqual(len(result), 3)  # -3, -2, -1

        expected_x = [-3, -2, -1]
        expected_y = [9, 4, 1]  # x^2

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_mathematical_constants(self):
        """Test equations with mathematical constants"""
        result = equation_to_df("pi*x", 0, 2, 1)

        expected_x = [0, 1, 2]
        expected_y = [0, np.pi, 2*np.pi]

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_almost_equal(result['y'].values, expected_y)

    def test_exponential_function(self):
        """Test exponential function: exp(x)"""
        result = equation_to_df("exp(x)", 0, 2, 1)

        expected_x = [0, 1, 2]
        expected_y = [1, np.e, np.e**2]

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_almost_equal(result['y'].values, expected_y)

    def test_absolute_value(self):
        """Test absolute value function: abs(x)"""
        result = equation_to_df("abs(x)", -2, 2, 1)

        expected_x = [-2, -1, 0, 1, 2]
        expected_y = [2, 1, 0, 1, 2]

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    # Error handling tests
    def test_error_non_string_equation(self):
        """Test error when equation is not a string"""
        with self.assertRaises(TypeError) as context:
            equation_to_df(123, 0, 5, 1)
        self.assertIn("方程必须是字符串类型", str(context.exception))

    def test_error_non_numeric_x_start(self):
        """Test error when x_start is not numeric"""
        with self.assertRaises(TypeError) as context:
            equation_to_df("x", "0", 5, 1)
        self.assertIn("x_start必须是数值类型", str(context.exception))

    def test_error_non_numeric_x_end(self):
        """Test error when x_end is not numeric"""
        with self.assertRaises(TypeError) as context:
            equation_to_df("x", 0, "5", 1)
        self.assertIn("x_end必须是数值类型", str(context.exception))

    def test_error_non_numeric_interval(self):
        """Test error when interval is not numeric"""
        with self.assertRaises(TypeError) as context:
            equation_to_df("x", 0, 5, "1")
        self.assertIn("interval必须是数值类型", str(context.exception))

    def test_error_negative_interval(self):
        """Test error when interval is negative or zero"""
        with self.assertRaises(ValueError) as context:
            equation_to_df("x", 0, 5, -1)
        self.assertIn("interval必须大于0", str(context.exception))

        with self.assertRaises(ValueError) as context:
            equation_to_df("x", 0, 5, 0)
        self.assertIn("interval必须大于0", str(context.exception))

    def test_error_invalid_range(self):
        """Test error when x_start >= x_end"""
        with self.assertRaises(ValueError) as context:
            equation_to_df("x", 5, 0, 1)
        self.assertIn("x_start必须小于x_end", str(context.exception))

        with self.assertRaises(ValueError) as context:
            equation_to_df("x", 5, 5, 1)
        self.assertIn("x_start必须小于x_end", str(context.exception))

    def test_error_invalid_equation_format(self):
        """Test error with invalid equation format"""
        with self.assertRaises(ValueError) as context:
            equation_to_df("y = x = 2", 0, 5, 1)
        self.assertIn("方程格式错误，应包含一个等号", str(context.exception))

    def test_error_invalid_expression(self):
        """Test error with invalid mathematical expression"""
        with self.assertRaises(ValueError) as context:
            equation_to_df("invalid_function(x)", 0, 5, 1)
        self.assertIn("表达式计算错误", str(context.exception))

    def test_error_division_by_zero(self):
        """Test error handling for division by zero"""
        with self.assertRaises(ValueError) as context:
            equation_to_df("1/x", -1, 1, 1)
        self.assertIn("计算x=0时出错", str(context.exception))

    def test_whitespace_handling(self):
        """Test that whitespace in equations is handled correctly"""
        result1 = equation_to_df("  y = 2 * x + 1  ", 0, 2, 1)
        result2 = equation_to_df("y=2*x+1", 0, 2, 1)

        # Both should produce the same result
        pd.testing.assert_frame_equal(result1, result2)

    def test_parentheses_handling(self):
        """Test equations with parentheses"""
        result = equation_to_df("2*(x + 1)", 0, 2, 1)

        expected_x = [0, 1, 2]
        expected_y = [2, 4, 6]  # 2*(x + 1)

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)

    def test_multiple_operations(self):
        """Test equation with multiple operations"""
        result = equation_to_df("x^3 - 2*x^2 + x - 1", 0, 3, 1)

        expected_x = [0, 1, 2, 3]
        # x=0: 0 - 0 + 0 - 1 = -1
        # x=1: 1 - 2 + 1 - 1 = -1
        # x=2: 8 - 8 + 2 - 1 = 1
        # x=3: 27 - 18 + 3 - 1 = 11
        expected_y = [-1, -1, 1, 11]

        np.testing.assert_array_equal(result['x'].values, expected_x)
        np.testing.assert_array_equal(result['y'].values, expected_y)


class TestCheckDates(unittest.TestCase):
    """Test cases for the check_dates function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create DataFrame with continuous dates
        self.df_continuous = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02', '2023-01-03', '2023-01-04', '2023-01-05'],
            'value': [10, 20, 30, 40, 50]
        })

        # Create DataFrame with missing dates
        self.df_missing = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02', '2023-01-04', '2023-01-06'],  # Missing 01-03, 01-05
            'value': [10, 20, 40, 60]
        })

        # Create DataFrame with duplicate dates
        self.df_duplicate = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02', '2023-01-02', '2023-01-03'],  # Duplicate 01-02
            'value': [10, 20, 25, 30]
        })

        # Create DataFrame with datetime objects
        self.df_datetime = pd.DataFrame({
            'date': [datetime(2023, 1, 1), datetime(2023, 1, 2), datetime(2023, 1, 3)],
            'value': [10, 20, 30]
        })

        # Create DataFrame with invalid dates
        self.df_invalid = pd.DataFrame({
            'date': ['2023-01-01', 'invalid-date', '2023-01-03'],
            'value': [10, 20, 30]
        })

        # Create empty DataFrame
        self.df_empty = pd.DataFrame({
            'date': [],
            'value': []
        })

    def test_continuous_dates_string_format(self):
        """Test with continuous dates in string format"""
        # Capture print output
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_continuous, 'date')

        output = f.getvalue()
        self.assertIn("日期序列是连续的，没有缺失的日期", output)
        self.assertIn("状态: ✓ 日期序列完整且连续", output)

    def test_continuous_dates_datetime_format(self):
        """Test with continuous dates in datetime format"""
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_datetime, 'date')

        output = f.getvalue()
        self.assertIn("日期序列是连续的，没有缺失的日期", output)
        self.assertIn("状态: ✓ 日期序列完整且连续", output)

    def test_missing_dates(self):
        """Test with missing dates"""
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_missing, 'date')

        output = f.getvalue()
        self.assertIn("发现 2 个缺失的日期", output)
        self.assertIn("2023-01-03", output)
        self.assertIn("2023-01-05", output)
        self.assertIn("状态: ✗ 日期序列存在缺失或重复", output)

    def test_duplicate_dates(self):
        """Test with duplicate dates"""
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_duplicate, 'date')

        output = f.getvalue()
        self.assertIn("发现 1 个重复的日期", output)
        self.assertIn("2023-01-02", output)
        self.assertIn("出现 2 次", output)
        self.assertIn("状态: ✗ 日期序列存在缺失或重复", output)

    def test_invalid_dates(self):
        """Test with invalid dates"""
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_invalid, 'date')

        output = f.getvalue()
        self.assertIn("发现 1 个无效日期", output)

    def test_empty_dataframe(self):
        """Test with empty DataFrame"""
        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_empty, 'date')

        output = f.getvalue()
        self.assertIn("日期列为空，无法检查连续性", output)

    def test_error_non_dataframe(self):
        """Test error when input is not a DataFrame"""
        with self.assertRaises(TypeError) as context:
            check_dates("not_a_dataframe", 'date')

        self.assertIn("df必须是一个有效的 DataFrame", str(context.exception))

    def test_error_column_not_exists(self):
        """Test error when column doesn't exist"""
        with self.assertRaises(ValueError) as context:
            check_dates(self.df_continuous, 'nonexistent_column')

        self.assertIn("列 'nonexistent_column' 不存在于DataFrame中", str(context.exception))

    def test_error_invalid_interval(self):
        """Test error with invalid interval"""
        with self.assertRaises(ValueError) as context:
            check_dates(self.df_continuous, 'date', 'W')

        self.assertIn("目前只支持interval='D'", str(context.exception))

    def test_original_dataframe_not_modified(self):
        """Test that original DataFrame is not modified"""
        original_df = self.df_continuous.copy()

        import io
        import contextlib
        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(self.df_continuous, 'date')

        # Check that original DataFrame is unchanged
        pd.testing.assert_frame_equal(self.df_continuous, original_df)

    def test_large_gap_dates(self):
        """Test with large gaps in dates"""
        df_large_gap = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-15'],  # 13 days gap
            'value': [10, 20]
        })

        import io
        import contextlib

        f = io.StringIO()
        with contextlib.redirect_stdout(f):
            check_dates(df_large_gap, 'date')

        output = f.getvalue()
        self.assertIn("发现 13 个缺失的日期", output)


if __name__ == '__main__':
    unittest.main()
