1. 项目本身的依赖：

```bash
pip install -r requirements.txt
```

2. 安装到本地library

```bash
pip install .

# 目前发现这个行不通(开发者模式): pip install -e .
# 先不弄这个开发者模式
```

如果要安装到python或conda虚拟环境，则在这个虚拟环境console中，进入到当前这个文件目录，然后执行上面的命令即可。

3. 升级代码到本地library

```bash
pip install --upgrade .

# 说明：如果用的py notebook jupyter server，jupyter server要重启才能生效
```

4. 环境变量

为了更简化方法的入参，增设以下环境变量，请根据实际情况修改：

```
PY_CHART_URL=画图的工具url，例如 https://branch.pugwoo.com
```
