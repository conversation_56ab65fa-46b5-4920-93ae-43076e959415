import pandas as pd
import numpy as np
import re
import math


def merge_df(*args):
    """
    合并多个df
    :param args: 参数列表必须是df, type, df, type, df, type 这样的组合，其中type是字符串，df必须是2列，第一列是日期或顺序，第二列是数值
    :return: 合并后的df
    """

    if len(args) % 2 != 0:
        raise ValueError("必须成对传入 df 和 type 参数")

    result_list = None

    for i in range(0, len(args), 2):
        df = args[i]
        type = args[i + 1]

        # 检查df是否为Pandas DataFrame
        if not isinstance(df, pd.DataFrame):
            raise TypeError(f"参数 {i} 不是一个有效的 DataFrame")

        df_copy = df.copy()
        df_copy.insert(1, 'type', type)

        if result_list is None:
            result_list = df_copy.values.tolist()
        else:
            result_list = result_list + df_copy.values.tolist()

    return pd.DataFrame(result_list, columns=['date', 'type', 'value'])

def check_dates(df, column_name, interval = "D"):
    """
    检查df的日期列是否是连续的（没有缺失的），对于不连续的日期，打印出信息，不会报错
    :param df: 不会修改原df
    :param column_name: 日期列，日期列支持yyyy-MM-dd字符串格式或datetime类型
    :param interval: 目前只支持D，检查每天是否连续
    :return:
    """

    # 参数验证
    if not isinstance(df, pd.DataFrame):
        raise TypeError("df必须是一个有效的 DataFrame")

    if column_name not in df.columns:
        raise ValueError(f"列 '{column_name}' 不存在于DataFrame中")

    if interval != "D":
        raise ValueError("目前只支持interval='D'（每日检查）")

    # 复制DataFrame以避免修改原始数据
    df_copy = df.copy()

    # 检查日期列是否为空
    if df_copy[column_name].empty:
        print("日期列为空，无法检查连续性")
        return

    # 转换日期列为datetime类型
    try:
        # 如果是字符串格式，尝试转换为datetime
        if df_copy[column_name].dtype == 'object':
            df_copy[column_name] = pd.to_datetime(df_copy[column_name], format='%Y-%m-%d', errors='coerce')
        elif not pd.api.types.is_datetime64_any_dtype(df_copy[column_name]):
            df_copy[column_name] = pd.to_datetime(df_copy[column_name], errors='coerce')
    except Exception as e:
        print(f"日期转换失败: {str(e)}")
        return

    # 检查是否有无效日期
    invalid_dates = df_copy[column_name].isna()
    if invalid_dates.any():
        invalid_count = invalid_dates.sum()
        print(f"发现 {invalid_count} 个无效日期，这些行将被忽略")

    # 移除无效日期
    valid_dates = df_copy[column_name].dropna()

    if valid_dates.empty:
        print("没有有效的日期数据")
        return

    # 排序日期
    valid_dates = valid_dates.sort_values()

    # 获取日期范围
    start_date = valid_dates.min()
    end_date = valid_dates.max()

    print(f"日期范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

    # 生成完整的日期序列
    expected_dates = pd.date_range(start=start_date, end=end_date, freq='D')

    # 找出缺失的日期
    actual_dates_set = set(valid_dates.dt.date)
    expected_dates_set = set(expected_dates.date)
    missing_dates = expected_dates_set - actual_dates_set

    if missing_dates:
        missing_dates_sorted = sorted(missing_dates)
        print(f"发现 {len(missing_dates)} 个缺失的日期:")

        # 如果缺失日期太多，只显示前10个和后10个
        if len(missing_dates_sorted) <= 20:
            for date in missing_dates_sorted:
                print(f"  - {date.strftime('%Y-%m-%d')}")
        else:
            print("  前10个缺失日期:")
            for date in missing_dates_sorted[:10]:
                print(f"    - {date.strftime('%Y-%m-%d')}")
            print(f"  ... (省略 {len(missing_dates_sorted) - 20} 个日期)")
            print("  后10个缺失日期:")
            for date in missing_dates_sorted[-10:]:
                print(f"    - {date.strftime('%Y-%m-%d')}")
    else:
        print("日期序列是连续的，没有缺失的日期")

    # 检查重复日期
    duplicate_dates = valid_dates[valid_dates.duplicated()]
    if not duplicate_dates.empty:
        unique_duplicates = duplicate_dates.dt.date.unique()
        print(f"发现 {len(unique_duplicates)} 个重复的日期:")
        for date in unique_duplicates:
            count = (valid_dates.dt.date == date).sum()
            print(f"  - {date.strftime('%Y-%m-%d')} (出现 {count} 次)")

    # 总结信息
    total_expected = len(expected_dates)
    total_actual = len(valid_dates.unique())
    print(f"\n总结:")
    print(f"  期望日期数量: {total_expected}")
    print(f"  实际日期数量: {total_actual}")
    print(f"  缺失日期数量: {len(missing_dates)}")
    print(f"  重复日期数量: {len(duplicate_dates)}")

    if len(missing_dates) == 0 and len(duplicate_dates) == 0:
        print("  状态: ✓ 日期序列完整且连续")
    else:
        print("  状态: ✗ 日期序列存在缺失或重复")


def equation_to_df(equation, x_start, x_end, interval):
    """
    将一个方程转换成df
    :param equation: 方程，例如 y = 2x + 1 或 y=x^2+1 或 y = sqrt(x)-1 等
    :param x_start: x的起始值
    :param x_end: x的结束值
    :param interval: x的间隔
    :return: df 两列，一列是x，一列是y
    """

    # 参数验证
    if not isinstance(equation, str):
        raise TypeError("方程必须是字符串类型")

    if not isinstance(x_start, (int, float)):
        raise TypeError("x_start必须是数值类型")

    if not isinstance(x_end, (int, float)):
        raise TypeError("x_end必须是数值类型")

    if not isinstance(interval, (int, float)):
        raise TypeError("interval必须是数值类型")

    if interval <= 0:
        raise ValueError("interval必须大于0")

    if x_start >= x_end:
        raise ValueError("x_start必须小于x_end")

    # 清理和标准化方程
    equation = equation.strip()

    # 移除 "y =" 部分，只保留右侧表达式
    if '=' in equation:
        parts = equation.split('=')
        if len(parts) != 2:
            raise ValueError("方程格式错误，应包含一个等号")
        expression = parts[1].strip()
    else:
        expression = equation

    # 预处理表达式，处理常见的数学符号
    expression = _preprocess_expression(expression)

    # 生成x值
    x_values = np.arange(x_start, x_end + interval, interval)

    # 计算对应的y值
    y_values = []
    for x in x_values:
        try:
            y = _evaluate_expression(expression, x)
            y_values.append(y)
        except Exception as e:
            raise ValueError(f"计算x={x}时出错: {str(e)}")

    # 创建DataFrame
    result_df = pd.DataFrame({
        'x': x_values,
        'y': y_values
    })

    return result_df


def _preprocess_expression(expression):
    """
    预处理数学表达式，将常见的数学符号转换为Python可识别的格式
    """
    # 移除所有空格
    expression = re.sub(r'\s+', '', expression)

    # 处理幂运算：x^2 -> x**2
    expression = re.sub(r'\^', '**', expression)

    # 先处理隐式乘法，再处理函数替换
    # 这样可以避免函数名被误认为是变量

    # 处理隐式乘法：2x -> 2*x, 3(x+1) -> 3*(x+1)
    # 数字后跟字母
    expression = re.sub(r'(\d)([a-zA-Z])', r'\1*\2', expression)

    # 字母或右括号后跟数字
    expression = re.sub(r'([a-zA-Z\)])(\d)', r'\1*\2', expression)

    # 数字后跟左括号
    expression = re.sub(r'(\d)(\()', r'\1*\2', expression)

    # 右括号后跟左括号
    expression = re.sub(r'(\))(\()', r'\1*\2', expression)

    # 变量后跟左括号（如 x(x+1) -> x*(x+1)）
    # 但要小心不要影响函数调用
    expression = re.sub(r'([a-zA-Z])(\()', r'\1*\2', expression)

    # 现在处理数学函数替换
    # 注意：由于上面的处理，函数调用现在变成了 func*(args)
    # 我们需要将其恢复为正确的函数调用格式

    math_functions = {
        'sqrt': 'math.sqrt',
        'sin': 'math.sin',
        'cos': 'math.cos',
        'tan': 'math.tan',
        'log': 'math.log',
        'ln': 'math.log',
        'exp': 'math.exp',
        'abs': 'abs',
        'pi': 'math.pi',
        'e': 'math.e'
    }

    for func, replacement in math_functions.items():
        if func in ['pi', 'e']:
            # 常量直接替换
            pattern = r'\b' + func + r'\b'
            expression = re.sub(pattern, replacement, expression)
        else:
            # 函数调用：func*( -> replacement(
            pattern = r'\b' + func + r'\*\('
            replacement_pattern = replacement + '('
            expression = re.sub(pattern, replacement_pattern, expression)

    return expression


def _evaluate_expression(expression, x):
    """
    安全地计算数学表达式的值
    """
    # 创建安全的命名空间，只包含必要的数学函数和常量
    safe_dict = {
        'x': x,
        'math': math,
        'abs': abs,
        '__builtins__': {}
    }

    try:
        result = eval(expression, safe_dict)

        # 检查结果是否为有效数值 (包括numpy类型)
        if not isinstance(result, (int, float, complex, np.integer, np.floating)):
            raise ValueError(f"表达式结果不是数值类型: {type(result)}")

        # 转换为标准Python类型
        if isinstance(result, (np.integer, np.floating)):
            result = float(result)

        # 检查是否为NaN或无穷大
        if isinstance(result, (int, float)) and (math.isnan(result) or math.isinf(result)):
            raise ValueError("表达式结果为NaN或无穷大")

        return float(result.real) if isinstance(result, complex) else float(result)

    except Exception as e:
        raise ValueError(f"表达式计算错误: {str(e)}")
