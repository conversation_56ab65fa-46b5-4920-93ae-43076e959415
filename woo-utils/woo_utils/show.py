# 负责画图相关的工作
import os
import sys

import numpy as np
import pandas as pd
import requests
from IPython.display import IFrame
from IPython.display import Markdown

def draw(req_body, width=1000, height=600):
    """
    画图工具，对接branch.pugwoo.com的画图工具
    :param height: iframe的高度
    :param width: iframe的宽度
    :param req_body: 请求到后端的json body
    :return: IFrame实例
    """
    chart_url = os.environ.get('PY_CHART_URL')
    if chart_url is None or chart_url == "":
        sys.stderr.write("missing env variable PY_CHART_URL\n")
        return

    # 如果req_body的data是df类型，则转换为list
    if req_body['data'] is not None and isinstance(req_body['data'], pd.DataFrame):
        req_body['data'] = req_body['data'].replace({np.nan: None}) # 将NaN处理成null
        req_body['data'] = req_body['data'].values.tolist()

    response = requests.post(chart_url + '/create_chart', json=req_body)
    url = chart_url + '/chart/' + response.json()['data']['linkCode']
    print(url)

    return IFrame(src=url, width=width, height=height)

def markdown(markdown):
    """
    输出为markdown格式
    :param markdown: markdown的内容
    :return: markdown渲染结果
    """
    return Markdown(markdown)