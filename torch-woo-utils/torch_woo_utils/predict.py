# PyTorch-based prediction algorithms
import pandas as pd
import numpy as np
from dateutil.relativedelta import relativedelta
import warnings

try:
    import torch
    from chronos import BaseChronosPipeline
    CHRONOS_AVAILABLE = True
except ImportError:
    CHRONOS_AVAILABLE = False


def detect_frequency_and_generate_dates(df_index, n):
    """
    检测时间序列的频率并生成相应的未来日期
    :param df_index: DataFrame的日期索引
    :param n: 要生成的未来日期数量
    :return: 未来日期列表
    """
    # 确保索引是datetime类型
    if not isinstance(df_index, pd.DatetimeIndex):
        df_index = pd.to_datetime(df_index)

    if len(df_index) < 2:
        # 如果只有一个日期，默认按天生成
        last_date = df_index[-1]
        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=n, freq='D')
        return future_dates

    # 计算日期间隔
    intervals = df_index[1:] - df_index[:-1]
    # 找到最常见的间隔
    interval_counts = intervals.value_counts()
    most_common_interval = interval_counts.index[0] if len(interval_counts) > 0 else intervals[0]

    last_date = df_index[-1]

    # 根据间隔类型生成未来日期
    if most_common_interval.days == 1:
        # 日频率
        future_dates = pd.date_range(start=last_date + pd.Timedelta(days=1), periods=n, freq='D')
    elif most_common_interval.days == 7:
        # 周频率
        future_dates = pd.date_range(start=last_date + pd.Timedelta(weeks=1), periods=n, freq='W')
    elif 28 <= most_common_interval.days <= 31:
        # 月频率
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + relativedelta(months=1)
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)
    elif 89 <= most_common_interval.days <= 92:
        # 季度频率
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + relativedelta(months=3)
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)
    elif 365 <= most_common_interval.days <= 366:
        # 年频率
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + relativedelta(years=1)
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)
    else:
        # 其他情况，使用检测到的间隔
        future_dates = []
        current_date = last_date
        for i in range(n):
            current_date = current_date + most_common_interval
            future_dates.append(current_date)
        future_dates = pd.DatetimeIndex(future_dates)

    return future_dates


def chronos_predict(df, n, model_name="amazon/chronos-t5-small", device="auto",
                   torch_dtype="auto", quantile_levels=None, include_confidence_intervals=False):
    """
    使用Amazon Chronos算法进行预测
    适用于各种时间序列预测任务，基于预训练的Transformer模型
    :param df: 预测输入，约定是两列，第一列是yyyy-MM-dd的日期格式；第二列是数值；df按时间顺序排列好
    :param n: 要预测的未来节点数n
    :param model_name: Chronos模型名称，可选值：
                      "amazon/chronos-t5-tiny", "amazon/chronos-t5-mini", "amazon/chronos-t5-small",
                      "amazon/chronos-t5-base", "amazon/chronos-t5-large"
                      或对应的Bolt版本："amazon/chronos-bolt-tiny", "amazon/chronos-bolt-small", 等
    :param device: 设备选择，"auto"表示自动选择（有GPU用GPU，否则用CPU），"cpu"强制使用CPU，"cuda"强制使用GPU
    :param torch_dtype: 数据类型，"auto"表示自动选择，也可以指定torch.float32, torch.bfloat16等
    :param quantile_levels: 分位数水平列表，例如[0.1, 0.5, 0.9]，如果为None则使用[0.1, 0.5, 0.9]
    :param include_confidence_intervals: 是否包含置信区间，默认False
    :return: 预测结果DataFrame，包含'index'和'predicted_mean'列；如果include_confidence_intervals=True，还包含'predicted_lower'和'predicted_upper'列
    """
    # 首先进行输入验证
    # 检查输入df是否正好有2列
    if len(df.columns) != 2:
        print(f"错误：输入数据必须包含2列（日期列和数值列），但实际包含 {len(df.columns)} 列")
        return None

    # 检查数据长度
    if len(df) < 2:
        print("错误：输入数据至少需要2个数据点")
        return None

    # 检查Chronos是否可用
    if not CHRONOS_AVAILABLE:
        raise ImportError("Chronos未安装。请运行: pip install chronos-forecasting torch")

    try:
        # 复制数据以避免修改原始数据
        df_copy = df.copy()

        # 设置默认分位数水平
        if quantile_levels is None:
            quantile_levels = [0.1, 0.5, 0.9]

        # 设备选择逻辑
        if device == "auto":
            if torch.cuda.is_available():
                device_map = "cuda"
            else:
                device_map = "cpu"
        else:
            device_map = device

        # 数据类型选择
        if torch_dtype == "auto":
            if device_map == "cuda":
                torch_dtype_actual = torch.bfloat16
            else:
                torch_dtype_actual = torch.float32
        else:
            torch_dtype_actual = torch_dtype

        # 加载Chronos模型
        print(f"正在加载Chronos模型: {model_name} (设备: {device_map})")
        pipeline = BaseChronosPipeline.from_pretrained(
            model_name,
            device_map=device_map,
            torch_dtype=torch_dtype_actual,
        )

        # 准备输入数据 - 只使用数值列
        values = df_copy.iloc[:, 1].values

        # 检查数据中是否有NaN或无穷大值
        if np.any(np.isnan(values)) or np.any(np.isinf(values)):
            print("警告：数据中包含NaN或无穷大值，将尝试处理")
            # 简单的前向填充处理NaN
            values = pd.Series(values).fillna(method='ffill').fillna(method='bfill').values
            # 处理无穷大值
            values = np.where(np.isinf(values), np.nan, values)
            values = pd.Series(values).fillna(method='ffill').fillna(method='bfill').values

        # 转换为torch tensor
        context_tensor = torch.tensor(values, dtype=torch.float32)

        # 进行预测
        print(f"正在进行预测，预测步数: {n}")
        quantiles, mean = pipeline.predict_quantiles(
            context=context_tensor,
            prediction_length=n,
            quantile_levels=quantile_levels,
        )

        # 生成未来日期
        future_dates = detect_frequency_and_generate_dates(df_copy.iloc[:, 0], n)

        # 创建预测结果DataFrame
        result_df = pd.DataFrame({
            'index': future_dates.strftime('%Y-%m-%d'),
            'predicted_mean': mean.numpy()
        })

        # 如果需要包含置信区间，添加置信区间列
        if include_confidence_intervals:
            # 使用第一个和最后一个分位数作为置信区间
            result_df['predicted_lower'] = quantiles[0].numpy()
            result_df['predicted_upper'] = quantiles[-1].numpy()

        print(f"预测完成，生成了 {len(result_df)} 个预测点")
        return result_df

    except Exception as e:
        print(f"Chronos预测过程中发生错误: {str(e)}")
        return None
