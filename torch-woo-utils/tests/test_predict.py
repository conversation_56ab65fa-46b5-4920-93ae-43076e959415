import unittest
import pandas as pd
import numpy as np
from datetime import datetime, date
import sys
import os

# Add the parent directory to the path to import woo_utils
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Import the specific module directly to avoid dependency issues
import importlib.util
spec = importlib.util.spec_from_file_location("predict", os.path.join(os.path.dirname(__file__), '..', 'torch_woo_utils', 'predict.py'))
predict = importlib.util.module_from_spec(spec)
spec.loader.exec_module(predict)

chronos_predict = predict.chronos_predict

class TestChronosPredict(unittest.TestCase):
    """Test cases for the chronos_predict function"""

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create sample daily data
        dates = pd.date_range('2023-01-01', periods=50, freq='D')
        values = 100 + np.sin(np.arange(50) * 2 * np.pi / 7) * 10 + np.random.normal(0, 2, 50)  # Weekly pattern with noise
        self.daily_df = pd.DataFrame({
            'date': dates.strftime('%Y-%m-%d'),
            'value': values
        })

        # Create sample monthly data
        monthly_dates = pd.date_range('2020-01-01', periods=24, freq='MS')  # 2 years of monthly data
        monthly_values = 1000 + np.sin(np.arange(24) * 2 * np.pi / 12) * 200 + np.random.normal(0, 50, 24)  # Yearly pattern
        self.monthly_df = pd.DataFrame({
            'date': monthly_dates.strftime('%Y-%m-%d'),
            'value': monthly_values
        })

        # Create simple trend data
        trend_dates = pd.date_range('2023-01-01', periods=30, freq='D')
        trend_values = 100 + np.arange(30) * 2 + np.random.normal(0, 5, 30)  # Linear trend
        self.trend_df = pd.DataFrame({
            'date': trend_dates.strftime('%Y-%m-%d'),
            'value': trend_values
        })

        # Create minimal data (edge case)
        self.minimal_df = pd.DataFrame({
            'date': ['2023-01-01', '2023-01-02'],
            'value': [100, 105]
        })

    def test_chronos_predict_basic_functionality(self):
        """Test basic Chronos prediction functionality"""
        try:
            result = chronos_predict(self.daily_df, n=7, model_name="amazon/chronos-t5-tiny", device="cpu")

            # Check structure
            self.assertIsNotNone(result)
            self.assertEqual(list(result.columns), ['index', 'predicted_mean'])
            self.assertEqual(len(result), 7)

            # Check that predictions are reasonable (not NaN or infinite)
            self.assertFalse(result['predicted_mean'].isna().any())
            self.assertTrue(np.isfinite(result['predicted_mean']).all())

            # Check date format
            self.assertTrue(all(len(date_str) == 10 for date_str in result['index']))
            self.assertTrue(all('-' in date_str for date_str in result['index']))

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")

    def test_chronos_predict_monthly_data(self):
        """Test Chronos prediction with monthly data"""
        try:
            result = chronos_predict(self.monthly_df, n=6, model_name="amazon/chronos-t5-tiny", device="cpu")

            # Check structure
            self.assertIsNotNone(result)
            self.assertEqual(len(result), 6)

            # Check that predictions are reasonable
            self.assertFalse(result['predicted_mean'].isna().any())
            self.assertTrue(np.isfinite(result['predicted_mean']).all())

            # Check that predicted values are in reasonable range
            original_mean = self.monthly_df['value'].mean()
            predicted_mean = result['predicted_mean'].mean()
            # Predictions should be within reasonable range (allow for more variance with Chronos)
            self.assertLess(abs(predicted_mean - original_mean) / original_mean, 1.0)

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")

    def test_chronos_predict_with_confidence_intervals(self):
        """Test Chronos prediction with confidence intervals"""
        try:
            result = chronos_predict(
                self.daily_df,
                n=5,
                model_name="amazon/chronos-t5-tiny",
                device="cpu",
                include_confidence_intervals=True,
                quantile_levels=[0.1, 0.5, 0.9]
            )

            # Check structure with confidence intervals
            self.assertIsNotNone(result)
            expected_columns = ['index', 'predicted_mean', 'predicted_lower', 'predicted_upper']
            self.assertEqual(list(result.columns), expected_columns)
            self.assertEqual(len(result), 5)

            # Check that all values are finite
            for col in ['predicted_mean', 'predicted_lower', 'predicted_upper']:
                self.assertFalse(result[col].isna().any())
                self.assertTrue(np.isfinite(result[col]).all())

            # Check that lower <= mean <= upper
            self.assertTrue((result['predicted_lower'] <= result['predicted_mean']).all())
            self.assertTrue((result['predicted_mean'] <= result['predicted_upper']).all())

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")

    def test_chronos_predict_different_models(self):
        """Test Chronos prediction with different model sizes"""
        model_names = ["amazon/chronos-t5-tiny", "amazon/chronos-t5-mini"]

        for model_name in model_names:
            with self.subTest(model=model_name):
                try:
                    result = chronos_predict(self.trend_df, n=3, model_name=model_name, device="cpu")

                    # Check basic structure
                    self.assertIsNotNone(result)
                    self.assertEqual(len(result), 3)
                    self.assertEqual(list(result.columns), ['index', 'predicted_mean'])

                    # Check that predictions are reasonable
                    self.assertFalse(result['predicted_mean'].isna().any())
                    self.assertTrue(np.isfinite(result['predicted_mean']).all())

                except ImportError as e:
                    self.skipTest(f"Chronos not available: {e}")
                except Exception as e:
                    # If model is not available, skip this specific model
                    self.skipTest(f"Model {model_name} not available: {e}")

    def test_chronos_predict_edge_cases(self):
        """Test Chronos prediction edge cases"""
        try:
            # Test with minimal data
            result = chronos_predict(self.minimal_df, n=2, model_name="amazon/chronos-t5-tiny", device="cpu")
            self.assertIsNotNone(result)
            self.assertEqual(len(result), 2)

            # Test with single column (should fail)
            single_col_df = pd.DataFrame({'value': [1, 2, 3]})
            result = chronos_predict(single_col_df, n=1, model_name="amazon/chronos-t5-tiny", device="cpu")
            self.assertIsNone(result)

            # Test with three columns (should fail)
            three_col_df = pd.DataFrame({
                'date': ['2023-01-01', '2023-01-02'],
                'value': [100, 105],
                'extra': [1, 2]
            })
            result = chronos_predict(three_col_df, n=1, model_name="amazon/chronos-t5-tiny", device="cpu")
            self.assertIsNone(result)

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")

    def test_chronos_predict_device_selection(self):
        """Test Chronos prediction device selection"""
        try:
            # Test CPU device
            result_cpu = chronos_predict(self.daily_df, n=3, model_name="amazon/chronos-t5-tiny", device="cpu")
            self.assertIsNotNone(result_cpu)

            # Test auto device selection
            result_auto = chronos_predict(self.daily_df, n=3, model_name="amazon/chronos-t5-tiny", device="auto")
            self.assertIsNotNone(result_auto)

            # Both should have same structure
            self.assertEqual(list(result_cpu.columns), list(result_auto.columns))
            self.assertEqual(len(result_cpu), len(result_auto))

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")

    def test_chronos_predict_data_immutability(self):
        """Test that Chronos prediction doesn't modify input data"""
        try:
            original_df = self.daily_df.copy()

            # Run prediction
            result = chronos_predict(self.daily_df, n=5, model_name="amazon/chronos-t5-tiny", device="cpu")

            # Check that result is valid
            self.assertIsNotNone(result)

            # Check that original DataFrame is unchanged
            pd.testing.assert_frame_equal(self.daily_df, original_df)

        except ImportError as e:
            self.skipTest(f"Chronos not available: {e}")
